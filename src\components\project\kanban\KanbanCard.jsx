'use client';

import React from 'react';
import Image from 'next/image';
import { Draggable } from '@hello-pangea/dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
	Calendar,
	MessageCircle,
	Paperclip,
	CheckSquare,
	Clock,
	AlertCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { getPriorityById } from '../data/kanban-data';

/**
 * KanbanCard Component
 * Individual card within a kanban column following Trello-inspired design
 */
export const KanbanCard = ({ card, index, onClick, className, ...props }) => {
	const priority = getPriorityById(card.priority);
	const isOverdue = card.dueDate && new Date(card.dueDate) < new Date();
	const isDueSoon =
		card.dueDate &&
		new Date(card.dueDate) <= new Date(Date.now() + 2 * 24 * 60 * 60 * 1000) &&
		!isOverdue;

	const handleCardClick = (e) => {
		e.stopPropagation();
		onClick?.(card);
	};

	const handleKeyDown = (e) => {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			handleCardClick(e);
		}
	};

	return (
		<Draggable draggableId={card.id} index={index}>
			{(provided, snapshot) => (
				<Card
					ref={provided.innerRef}
					{...provided.draggableProps}
					{...provided.dragHandleProps}
					className={cn(
						// Base styles following design system
						'cursor-pointer',
						'bg-white border-2 border-transparent rounded-lg shadow-sm',
						'hover:border-2 hover:border-muted-foreground',

						// Dragging state
						snapshot.isDragging && 'opacity-90 shadow-xl scale-105',

						// Custom className
						className
					)}
					onClick={handleCardClick}
					onKeyDown={handleKeyDown}
					tabIndex={0}
					role="button"
					aria-label={`Card: ${card.title}${card.description ? `. ${card.description}` : ''}`}
					aria-pressed={snapshot.isDragging}
					{...props}
				>
					{/* Cover Image */}
					{card.coverImage && (
						<div className="relative w-full h-32 overflow-hidden rounded-t-lg">
							<Image
								src={card.coverImage}
								alt={card.title}
								width={400}
								height={128}
								className="w-full h-full object-cover transition-all duration-300"
								sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
								priority={false}
							/>
							<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 transition-opacity duration-300" />
						</div>
					)}

					<CardContent className="p-3 space-y-3">
						{/* Title */}
						<h3 className="text-sm font-medium text-gray-800 line-clamp-2 leading-tight">
							{card.title}
						</h3>

						{/* Description */}
						{card.description && (
							<p className="text-xs text-gray-600 line-clamp-2 leading-relaxed">
								{card.description}
							</p>
						)}
					</CardContent>
				</Card>
			)}
		</Draggable>
	);
};
