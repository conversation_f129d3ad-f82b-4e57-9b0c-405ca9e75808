'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
	Bold,
	Italic,
	List,
	ListOrdered,
	Link as LinkIcon,
	Quote,
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * RichTextEditor Component
 * Simple textarea-based editor for descriptions and comments
 */
export const RichTextEditor = ({
	content = '',
	onChange,
	placeholder = 'Start typing...',
	compact = false,
	className,
}) => {
	const [textareaValue, setTextareaValue] = useState(content);

	// Update textarea value when content prop changes
	useEffect(() => {
		setTextareaValue(content);
	}, [content]);

	// Textarea handler
	const handleTextareaChange = (e) => {
		const value = e.target.value;
		setTextareaValue(value);
		onChange?.(value);
	};

	return (
		<div className="border rounded-md overflow-hidden">
			<Textarea
				value={textareaValue}
				onChange={handleTextareaChange}
				placeholder={placeholder}
				className={cn(
					'border-none resize-none focus-visible:ring-0',
					compact ? 'min-h-[60px]' : 'min-h-[120px]',
					className
				)}
			/>
			{!compact && (
				<div className="flex items-center justify-between p-2 border-t bg-gray-50">
					<div className="flex items-center gap-1">
						<Button
							variant="ghost"
							size="sm"
							className="h-6 w-6 p-0 opacity-50"
							disabled
						>
							<Bold className="h-3 w-3" />
						</Button>
						<Button
							variant="ghost"
							size="sm"
							className="h-6 w-6 p-0 opacity-50"
							disabled
						>
							<Italic className="h-3 w-3" />
						</Button>
						<Button
							variant="ghost"
							size="sm"
							className="h-6 w-6 p-0 opacity-50"
							disabled
						>
							<List className="h-3 w-3" />
						</Button>
					</div>
					<div className="text-xs text-gray-500">Basic text editor</div>
				</div>
			)}
		</div>
	);
};
