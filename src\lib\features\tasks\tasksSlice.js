import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	tasks: [],
	taskDetails: null,
	isLoading: false,
	error: null,
};

export const fetchTaskOfProject = createAsyncThunk(
	'tasks/fetchTaskOfProject',
	async (projectId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/tasks/project/${projectId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const fetchTaskAssignedToEmployee = createAsyncThunk(
	'tasks/fetchTaskAssignedToEmployee',
	async (employeeId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/tasks/assignees/${employeeId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const createTask = createAsyncThunk(
	'tasks/createTask',
	async (taskData, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.post('/tasks', taskData);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const updateTask = createAsyncThunk(
	'tasks/updateTask',
	async ({ taskData }, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.patch(`/tasks`, taskData);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const updateStatus = createAsyncThunk(
	'tasks/updateStatus',
	async ({ taskData }, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.patch(
				`/tasks/update-status`,
				taskData
			);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const fetchSingleTaskDetails = createAsyncThunk(
	'tasks/fetchSingleTaskDetails',
	async (taskId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/tasks/${taskId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

const tasksSlice = createSlice({
	name: 'tasks',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchTaskOfProject.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchTaskOfProject.fulfilled, (state, action) => {
				state.isLoading = false;
				state.tasks = action.payload.data;
			})
			.addCase(fetchTaskOfProject.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload;
			})
			.addCase(fetchTaskAssignedToEmployee.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchTaskAssignedToEmployee.fulfilled, (state, action) => {
				state.isLoading = false;
				state.tasks = action.payload;
			})
			.addCase(fetchTaskAssignedToEmployee.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload;
			})
			.addCase(createTask.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(createTask.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(createTask.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateTask.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateTask.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateTask.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateStatus.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateStatus.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateStatus.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchSingleTaskDetails.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchSingleTaskDetails.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.taskDetails = payload.data;
			})
			.addCase(fetchSingleTaskDetails.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export default tasksSlice.reducer;
