'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DragDropContext } from '@hello-pangea/dnd';
import { cn } from '@/lib/utils';
import { KanbanHeader } from './KanbanHeader';
import { KanbanColumn } from './KanbanColumn';
import { sampleProjects } from '../data/sample-projects';
import { getKanbanDataByProjectId } from '../data/kanban-data';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, X } from 'lucide-react';

/**
 * KanbanBoard Component
 * Main kanban board container with horizontal scrolling columns
 */
export const KanbanBoard = ({
	projectId,
	isGlassMode = false,
	className,
	...props
}) => {
	const router = useRouter();
	const [project, setProject] = useState(null);
	const [kanbanData, setKanbanData] = useState(null);
	const [loading, setLoading] = useState(true);
	const [openDropdownId, setOpenDropdownId] = useState(null);
	const [isAddingColumn, setIsAddingColumn] = useState(false);
	const [newColumnTitle, setNewColumnTitle] = useState('');

	// Load project and kanban data
	useEffect(() => {
		const loadData = () => {
			try {
				// Find project by ID
				const foundProject = sampleProjects.find(
					(p) => p.id === parseInt(projectId)
				);
				if (!foundProject) {
					console.error('Project not found:', projectId);
					setLoading(false);
					return;
				}

				// Get kanban data for project
				const foundKanbanData = getKanbanDataByProjectId(parseInt(projectId));
				if (!foundKanbanData) {
					console.error('Kanban data not found for project:', projectId);
					setLoading(false);
					return;
				}

				setProject(foundProject);
				setKanbanData(foundKanbanData);
				setLoading(false);
			} catch (error) {
				console.error('Error loading kanban data:', error);
				setLoading(false);
			}
		};

		if (projectId) {
			loadData();
		}
	}, [projectId]);

	// Drag and drop event handlers for @hello-pangea/dnd
	const handleDragEnd = (result) => {
		const { destination, source, draggableId } = result;

		// If no destination, do nothing
		if (!destination) return;

		// If dropped in the same position, do nothing
		if (
			destination.droppableId === source.droppableId &&
			destination.index === source.index
		) {
			return;
		}

		const sourceColumnId = source.droppableId;
		const destinationColumnId = destination.droppableId;

		// Moving within the same column
		if (sourceColumnId === destinationColumnId) {
			const column = kanbanData.columns.find(
				(col) => col.id === sourceColumnId
			);
			if (!column) return;

			const newCards = Array.from(column.cards);
			const [movedCard] = newCards.splice(source.index, 1);
			newCards.splice(destination.index, 0, movedCard);

			setKanbanData((prevData) => ({
				...prevData,
				columns: prevData.columns.map((col) =>
					col.id === sourceColumnId ? { ...col, cards: newCards } : col
				),
			}));
		} else {
			// Moving between different columns
			const sourceColumn = kanbanData.columns.find(
				(col) => col.id === sourceColumnId
			);
			const destinationColumn = kanbanData.columns.find(
				(col) => col.id === destinationColumnId
			);

			if (!sourceColumn || !destinationColumn) return;

			const sourceCards = Array.from(sourceColumn.cards);
			const destinationCards = Array.from(destinationColumn.cards);
			const [movedCard] = sourceCards.splice(source.index, 1);
			destinationCards.splice(destination.index, 0, movedCard);

			setKanbanData((prevData) => ({
				...prevData,
				columns: prevData.columns.map((col) => {
					if (col.id === sourceColumnId) {
						return { ...col, cards: sourceCards };
					}
					if (col.id === destinationColumnId) {
						return { ...col, cards: destinationCards };
					}
					return col;
				}),
			}));
		}
	};

	const handleCardClick = (card) => {
		// TODO: Open card detail modal
		console.log('Card clicked:', card);
	};

	const handleAddCard = (columnId, cardData) => {
		const newCard = {
			id: `card-${Date.now()}`, // Simple ID generation
			title: cardData.title,
			description: '',
			priority: 'medium',
			assignees: [],
			dueDate: null,
			tags: [],
			attachments: 0,
			comments: 0,
			checklist: { completed: 0, total: 0 },
		};

		setKanbanData((prevData) => {
			const newColumns = prevData.columns.map((column) => {
				if (column.id === columnId) {
					return {
						...column,
						cards: [...column.cards, newCard],
					};
				}
				return column;
			});

			return {
				...prevData,
				columns: newColumns,
			};
		});
	};

	const handleColumnMenuClick = (column) => {
		// TODO: Implement column menu
		console.log('Column menu clicked:', column);
	};

	const handleAddColumn = () => {
		if (newColumnTitle.trim()) {
			const newColumn = {
				id: `column-${Date.now()}`,
				title: newColumnTitle.trim(),
				color: isGlassMode
					? 'rgba(255, 255, 255, 0.1)'
					: 'rgba(255, 255, 255, 0.9)', // Match header background
				cards: [],
			};

			setKanbanData((prevData) => ({
				...prevData,
				columns: [...prevData.columns, newColumn],
			}));

			setNewColumnTitle('');
			setIsAddingColumn(false);
		}
	};

	const handleCancelAddColumn = () => {
		setNewColumnTitle('');
		setIsAddingColumn(false);
	};

	const handleKeyPress = (e) => {
		if (e.key === 'Enter') {
			handleAddColumn();
		} else if (e.key === 'Escape') {
			handleCancelAddColumn();
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center">
				<div className="text-center">
					<div className="inline-flex items-center gap-3 text-white">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
						<span className="text-lg animate-pulse">
							Loading kanban board...
						</span>
					</div>
				</div>
			</div>
		);
	}

	if (!project || !kanbanData) {
		return (
			<div className="min-h-screen bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center">
				<div className="text-white text-lg">Project not found</div>
			</div>
		);
	}

	return (
		<DragDropContext onDragEnd={handleDragEnd}>
			<div className="flex gap-3 min-w-max">
				{kanbanData.columns.map((column, index) => (
					<div
						key={column.id}
						// className="animate-in slide-in-from-bottom duration-300"
						// style={{ animationDelay: `${index * 100}ms` }}
					>
						<KanbanColumn
							column={column}
							cards={column.cards}
							onCardClick={handleCardClick}
							onAddCard={handleAddCard}
							onColumnMenuClick={handleColumnMenuClick}
							isGlassMode={isGlassMode}
							isDropdownOpen={openDropdownId === column.id}
							onDropdownOpenChange={(isOpen) =>
								setOpenDropdownId(isOpen ? column.id : null)
							}
						/>
					</div>
				))}

				{/* Add a List Column */}
				<div className="w-[17rem] flex-shrink-0">
					<Card
						className={cn(
							// Conditional styling based on glass mode
							isGlassMode
								? 'bg-white/10 backdrop-blur-md border border-white/20'
								: 'border-gray-200',
							'shadow-sm p-3 transition-all duration-300'
						)}
						style={{
							backgroundColor: !isGlassMode
								? 'rgba(255, 255, 255, 0.9)'
								: undefined,
						}}
					>
						{isAddingColumn ? (
							<div className="space-y-2">
								<Input
									value={newColumnTitle}
									onChange={(e) => setNewColumnTitle(e.target.value)}
									onKeyDown={handleKeyPress}
									placeholder="Enter list title..."
									className="h-8 text-sm"
									autoFocus
								/>
								<div className="flex gap-2">
									<Button
										size="sm"
										onClick={handleAddColumn}
										className="h-7 px-3 text-xs"
									>
										Add List
									</Button>
									<Button
										variant="ghost"
										size="sm"
										onClick={handleCancelAddColumn}
										className="h-7 px-2 text-xs"
									>
										<X className="h-3 w-3" />
									</Button>
								</div>
							</div>
						) : (
							<Button
								variant="ghost"
								onClick={() => setIsAddingColumn(true)}
								className={cn(
									'w-full justify-start text-left h-8',
									isGlassMode
										? 'text-white/70 hover:text-white hover:bg-white/20'
										: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
								)}
							>
								<Plus className="h-4 w-4 mr-2" />
								Add a list
							</Button>
						)}
					</Card>
				</div>
			</div>
		</DragDropContext>
	);
};
