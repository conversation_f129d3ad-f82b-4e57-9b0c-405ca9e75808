'use client';

import { useEffect, useState } from 'react';
import {
	KanbanBoard,
	KanbanHeader,
	sampleProjects,
} from '@/components/project';
import { getKanbanDataByProjectId } from '@/components/project/data/kanban-data';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { fetchSingleProjectDetails } from '@/lib/features/projects/projectsSlice';
import { useAppDispatch } from '@/lib/hooks';

export default function ProjectKanbanPage({ params }) {
	const { projectId } = params;
	const router = useRouter();
	const dispatch = useAppDispatch();
	const project = sampleProjects.find((p) => p.id === parseInt(projectId));
	const kanbanData = getKanbanDataByProjectId(parseInt(projectId));
	const [isGlassMode, setIsGlassMode] = useState(false);

	useEffect(() => {
		dispatch(fetchSingleProjectDetails(projectId));
	}, [dispatch, projectId]);

	const getBackgroundClass = () => {
		if (!project) return 'bg-gradient-to-br from-purple-500 to-blue-600';

		if (project.backgroundType === 'color' && project.color) {
			return project.color;
		}

		return '';
	};

	const getBackgroundStyle = () => {
		if (!project) return {};

		if (project.backgroundType === 'image' && project.backgroundImage) {
			return {
				backgroundImage: `url(${project.backgroundImage.url})`,
				backgroundSize: 'cover',
				backgroundPosition: 'center',
				backgroundRepeat: 'no-repeat',
				backgroundAttachment: 'fixed',
			};
		} else if (project.backgroundType === 'color' && project.color) {
			return {};
		}

		return {};
	};

	const handleBackClick = () => {
		router.push('/client-admin/projects-tasks-module/projects-kanban');
	};

	return (
		<div
			className={cn(
				'h-[calc(100dvh-5rem)] rounded-bl-lg rounded-br-lg',
				getBackgroundClass()
			)}
			style={getBackgroundStyle()}
		>
			<div className="bg-black/20 h-full flex flex-col">
				{/* Board Header */}
				<KanbanHeader
					project={project}
					kanbanData={kanbanData}
					onBackClick={handleBackClick}
					onSettingsClick={() => console.log('Settings clicked')}
					onFilterClick={() => console.log('Filter clicked')}
					onMembersClick={() => console.log('Members clicked')}
					onMenuClick={() => console.log('Menu clicked')}
					onShareClick={() => console.log('Share clicked')}
					onCalendarClick={() => console.log('Calendar clicked')}
					onAnalyticsClick={() => console.log('Analytics clicked')}
					onGlassModeToggle={() => setIsGlassMode(!isGlassMode)}
					isGlassMode={isGlassMode}
				/>
				<div className="flex-1 w-[calc(100dvw-5rem)] p-3 rounded-bl-lg rounded-br-lg overflow-auto scrollbar-thin scrollbar-thumb-muted hover:scrollbar-thumb-muted-foreground scrollbar-track-transparent">
					<div className="min-w-max">
						<KanbanBoard projectId={projectId} isGlassMode={isGlassMode} />
					</div>
				</div>
			</div>
		</div>
	);
}
